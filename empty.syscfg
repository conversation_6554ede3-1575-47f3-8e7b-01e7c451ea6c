/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();
const UART3   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 5;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

GPIO1.$name                              = "LED1";
GPIO1.port                               = "PORTA";
GPIO1.associatedPins[0].$name            = "PIN_0";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].assignedPin      = "0";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                         = "KEY";
GPIO2.port                          = "PORTA";
GPIO2.associatedPins[0].direction   = "INPUT";
GPIO2.associatedPins[0].polarity    = "RISE";
GPIO2.associatedPins[0].assignedPin = "18";
GPIO2.associatedPins[0].$name       = "UserKEY";

GPIO3.$name                         = "OLED_RST";
GPIO3.associatedPins[0].$name       = "PIN_RST";
GPIO3.associatedPins[0].pin.$assign = "PB14";

GPIO4.$name                         = "OLED_DC";
GPIO4.associatedPins[0].$name       = "PIN_DC";
GPIO4.associatedPins[0].pin.$assign = "PB15";

GPIO5.$name                         = "OLED_SCL";
GPIO5.associatedPins[0].$name       = "PIN_SCL";
GPIO5.associatedPins[0].pin.$assign = "PA28";

GPIO6.$name                         = "OLED_SDA";
GPIO6.associatedPins[0].$name       = "PIN_SDA";
GPIO6.associatedPins[0].pin.$assign = "PA31";

PWM1.$name                      = "PWM_0";
PWM1.ccIndex                    = [0];
PWM1.clockPrescale              = 80;
PWM1.timerStartTimer            = true;
PWM1.pwmMode                    = "EDGE_ALIGN_UP";
PWM1.clockDivider               = 2;
PWM1.timerCount                 = 10000;
PWM1.peripheral.ccp0Pin.$assign = "PB17";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";

PWM2.$name                      = "PWM_1";
PWM2.ccIndex                    = [1];
PWM2.clockPrescale              = 80;
PWM2.timerStartTimer            = true;
PWM2.pwmMode                    = "EDGE_ALIGN_UP";
PWM2.clockDivider               = 2;
PWM2.timerCount                 = 10000;
PWM2.peripheral.$assign         = "TIMG7";
PWM2.peripheral.ccp1Pin.$assign = "PB16";
PWM2.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";
PWM2.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable      = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";
SYSTICK.period            = 16777216;

TIMER1.$name             = "TIMER_0";
TIMER1.timerClkDiv       = 8;
TIMER1.timerClkPrescale  = 200;
TIMER1.timerStartTimer   = true;
TIMER1.timerMode         = "PERIODIC";
TIMER1.interrupts        = ["ZERO"];
TIMER1.interruptPriority = "0";
TIMER1.timerPeriod       = "10 ms";

UART1.$name                    = "UART_0";
UART1.enabledInterrupts        = ["RX"];
UART1.targetBaudRate           = 115200;
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                    = "UART_1";
UART2.rxFifoThreshold          = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART2.enableDMARX              = false;
UART2.enableDMATX              = false;
UART2.enabledInterrupts        = ["RX"];
UART2.rxTimeoutValue           = 15;
UART2.peripheral.rxPin.$assign = "PB7";
UART2.peripheral.txPin.$assign = "PB6";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric5";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";

UART3.$name                       = "UART_2";
UART3.rxFifoThreshold             = "DL_UART_RX_FIFO_LEVEL_3_4_FULL";
UART3.rxTimeoutValue              = 15;
UART3.enabledDMARXTriggers        = "DL_UART_DMA_INTERRUPT_RX";
UART3.enabledInterrupts           = ["RX"];
UART3.enableDMARX                 = false;
UART3.peripheral.rxPin.$assign    = "PB3";
UART3.peripheral.txPin.$assign    = "PB2";
UART3.txPinConfig.$name           = "ti_driverlib_gpio_GPIOPinGeneric4";
UART3.rxPinConfig.$name           = "ti_driverlib_gpio_GPIOPinGeneric7";
UART3.DMA_CHANNEL_RX.$name        = "DMA_CH0";
UART3.DMA_CHANNEL_RX.srcLength    = "BYTE";
UART3.DMA_CHANNEL_RX.dstLength    = "BYTE";
UART3.DMA_CHANNEL_RX.addressMode  = "f2b";
UART3.DMA_CHANNEL_RX.transferMode = "FULL_CH_REPEAT_SINGLE";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution     = "PA0";
Board.peripheral.$suggestSolution                = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution       = "PA20";
Board.peripheral.swdioPin.$suggestSolution       = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution     = "PA18";
PWM1.peripheral.$suggestSolution                 = "TIMA1";
TIMER1.peripheral.$suggestSolution               = "TIMA0";
UART2.peripheral.$suggestSolution                = "UART1";
UART3.peripheral.$suggestSolution                = "UART3";
UART3.DMA_CHANNEL_RX.peripheral.$suggestSolution = "DMA_CH0";
