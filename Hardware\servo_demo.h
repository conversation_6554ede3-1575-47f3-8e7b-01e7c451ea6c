#ifndef _SERVO_DEMO_H
#define _SERVO_DEMO_H

#include "motor.h"

// 舵机演示模式枚举
typedef enum {
    DEMO_MANUAL,        // 手动控制模式
    DEMO_SWEEP,         // 扫描模式
    DEMO_STEP,          // 步进模式
    DEMO_RANDOM         // 随机模式
} ServoDemo_Mode_t;

// 舵机演示控制结构体
typedef struct {
    ServoDemo_Mode_t mode;
    float angle1;
    float angle2;
    float target_angle1;
    float target_angle2;
    uint16_t step_counter;
    uint16_t delay_counter;
} ServoDemo_t;

// 函数声明
void ServoDemo_Init(ServoDemo_t* demo);
void ServoDemo_Update(ServoDemo_t* demo);
void ServoDemo_SetMode(ServoDemo_t* demo, ServoDemo_Mode_t mode);
void ServoDemo_SweepMode(ServoDemo_t* demo);
void ServoDemo_StepMode(ServoDemo_t* demo);
void ServoDemo_RandomMode(ServoDemo_t* demo);

#endif
