/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
#include "board.h"
#include "stdio.h"
#include "oled.h"
#include "bsp_key.h"
#include "motor.h"
int a=0;
int main(void)
{
	SYSCFG_DL_init();
	DL_Timer_startCounter(PWM_0_INST);//    PWM   
	DL_Timer_startCounter(PWM_1_INST);//    PWM   
	NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
	NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN);
	NVIC_ClearPendingIRQ(UART_2_INST_INT_IRQN);
	//使能串口中断
	NVIC_EnableIRQ(UART_1_INST_INT_IRQN);
	NVIC_EnableIRQ(UART_2_INST_INT_IRQN);
	NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);  
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);        
	OLED_Init();

	
    while (1) 
    {
		delay_ms(100);
		a++;
    }
}

// MG996R舵机角度控制变量
float servo_angle = 90.0f;  // 初始角度90度(中位)
pKeyInterface_t key = &UserKey;
UserKeyState_t keystate ;

void TIMER_0_INST_IRQHandler(void)
{
    if(DL_TimerA_getPendingInterrupt(TIMER_0_INST))
    {
        if(DL_TIMER_IIDX_ZERO)
        {
            keystate= key->getKeyState(100);

            // 按键控制舵机角度
            if(keystate==USEKEY_single_click) {
                servo_angle += 10.0f;  // 单击增加10度
                if(servo_angle > 180.0f) servo_angle = 180.0f;  // 限制最大角度
            }
            if(keystate==USEKEY_double_click) {
                servo_angle -= 10.0f;  // 双击减少10度
                if(servo_angle < 0.0f) servo_angle = 0.0f;      // 限制最小角度
            }
            if(keystate==USEKEY_long_click) {
                servo_angle = 90.0f;   // 长按回到中位
            }

            // 设置两个舵机到相同角度
            Set_Servo_Angle(servo_angle, servo_angle);
        }
    }
}







