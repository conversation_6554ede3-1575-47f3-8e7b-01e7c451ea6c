/*
 * MG996R舵机控制示例程序
 * 
 * 功能说明：
 * - 单击按键：切换演示模式
 * - 双击按键：在手动模式下调整角度
 * - 长按按键：回到中位（90度）
 * 
 * 演示模式：
 * 1. 手动控制模式
 * 2. 扫描模式（0-180度连续扫描）
 * 3. 步进模式（固定位置切换）
 * 4. 随机模式（随机位置移动）
 */

#include "board.h"
#include "stdio.h"
#include "oled.h"
#include "bsp_key.h"
#include "motor.h"
#include "servo_demo.h"

// 全局变量
ServoDemo_t servo_demo;
pKeyInterface_t key = &UserKey;
UserKeyState_t keystate;
uint16_t display_update_counter = 0;

// 模式名称字符串
const char* mode_names[] = {
    "Manual",
    "Sweep",
    "Step",
    "Random"
};

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    DL_Timer_startCounter(PWM_0_INST);    // 启动PWM输出
    DL_Timer_startCounter(PWM_1_INST);    // 启动PWM输出
    
    // 中断配置
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN);
    NVIC_ClearPendingIRQ(UART_2_INST_INT_IRQN);
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);
    NVIC_EnableIRQ(UART_2_INST_INT_IRQN);
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
    
    // OLED显示初始化
    OLED_Init();
    OLED_Clear();
    OLED_ShowString(0, 0, (uint8_t*)"MG996R Servo Demo");
    OLED_ShowString(0, 2, (uint8_t*)"Mode: Manual");
    OLED_ShowString(0, 4, (uint8_t*)"Angle1: 90");
    OLED_ShowString(0, 6, (uint8_t*)"Angle2: 90");
    
    // 舵机演示初始化
    ServoDemo_Init(&servo_demo);
    
    printf("MG996R舵机控制演示程序启动\r\n");
    printf("按键功能：\r\n");
    printf("单击 - 切换模式\r\n");
    printf("双击 - 手动调整角度\r\n");
    printf("长按 - 回到中位\r\n");
    
    while (1) 
    {
        delay_ms(10);  // 10ms主循环
        
        // 更新显示（每100ms更新一次）
        display_update_counter++;
        if(display_update_counter >= 10) {
            display_update_counter = 0;
            
            // 更新OLED显示
            char buffer[20];
            
            // 显示当前模式
            sprintf(buffer, "Mode: %s", mode_names[servo_demo.mode]);
            OLED_ShowString(0, 2, (uint8_t*)buffer);
            
            // 显示角度
            sprintf(buffer, "Angle1: %.0f", servo_demo.angle1);
            OLED_ShowString(0, 4, (uint8_t*)buffer);
            
            sprintf(buffer, "Angle2: %.0f", servo_demo.angle2);
            OLED_ShowString(0, 6, (uint8_t*)buffer);
            
            // 串口输出状态
            printf("模式: %s, 角度1: %.1f°, 角度2: %.1f°\r\n", 
                   mode_names[servo_demo.mode], servo_demo.angle1, servo_demo.angle2);
        }
    }
}

// 定时器中断处理函数
void TIMER_0_INST_IRQHandler(void)
{
    if(DL_TimerA_getPendingInterrupt(TIMER_0_INST))
    {
        if(DL_TIMER_IIDX_ZERO)
        {
            // 按键检测
            keystate = key->getKeyState(100);
            
            if(keystate == USEKEY_single_click) {
                // 单击：切换演示模式
                ServoDemo_Mode_t next_mode = (servo_demo.mode + 1) % 4;
                ServoDemo_SetMode(&servo_demo, next_mode);
                printf("切换到模式: %s\r\n", mode_names[next_mode]);
            }
            else if(keystate == USEKEY_double_click) {
                // 双击：在手动模式下调整角度
                if(servo_demo.mode == DEMO_MANUAL) {
                    servo_demo.angle1 += 15.0f;
                    servo_demo.angle2 -= 15.0f;
                    
                    if(servo_demo.angle1 > 180.0f) servo_demo.angle1 = 0.0f;
                    if(servo_demo.angle2 < 0.0f) servo_demo.angle2 = 180.0f;
                    
                    printf("手动调整角度: %.1f°, %.1f°\r\n", servo_demo.angle1, servo_demo.angle2);
                }
            }
            else if(keystate == USEKEY_long_click) {
                // 长按：回到中位
                servo_demo.angle1 = 90.0f;
                servo_demo.angle2 = 90.0f;
                printf("回到中位: 90°, 90°\r\n");
            }
            
            // 更新舵机演示状态
            ServoDemo_Update(&servo_demo);
        }
    }
}
