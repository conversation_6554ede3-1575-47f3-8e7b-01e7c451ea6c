#include "servo_demo.h"
#include <stdlib.h>
#include <math.h>

// 初始化舵机演示
void ServoDemo_Init(ServoDemo_t* demo)
{
    demo->mode = DEMO_MANUAL;
    demo->angle1 = 90.0f;
    demo->angle2 = 90.0f;
    demo->target_angle1 = 90.0f;
    demo->target_angle2 = 90.0f;
    demo->step_counter = 0;
    demo->delay_counter = 0;
    
    // 设置初始位置
    Set_Servo_Angle(demo->angle1, demo->angle2);
}

// 更新舵机演示状态
void ServoDemo_Update(ServoDemo_t* demo)
{
    switch(demo->mode) {
        case DEMO_MANUAL:
            // 手动模式不需要自动更新
            break;
            
        case DEMO_SWEEP:
            ServoDemo_SweepMode(demo);
            break;
            
        case DEMO_STEP:
            ServoDemo_StepMode(demo);
            break;
            
        case DEMO_RANDOM:
            ServoDemo_RandomMode(demo);
            break;
    }
    
    // 更新舵机位置
    Set_Servo_Angle(demo->angle1, demo->angle2);
}

// 设置演示模式
void ServoDemo_SetMode(ServoDemo_t* demo, ServoDemo_Mode_t mode)
{
    demo->mode = mode;
    demo->step_counter = 0;
    demo->delay_counter = 0;
    
    // 根据模式设置初始状态
    switch(mode) {
        case DEMO_MANUAL:
            demo->angle1 = 90.0f;
            demo->angle2 = 90.0f;
            break;
            
        case DEMO_SWEEP:
            demo->angle1 = 0.0f;
            demo->angle2 = 0.0f;
            break;
            
        case DEMO_STEP:
            demo->angle1 = 0.0f;
            demo->angle2 = 180.0f;
            break;
            
        case DEMO_RANDOM:
            demo->angle1 = 90.0f;
            demo->angle2 = 90.0f;
            break;
    }
}

// 扫描模式：舵机在0-180度之间连续扫描
void ServoDemo_SweepMode(ServoDemo_t* demo)
{
    static float direction1 = 1.0f;
    static float direction2 = -1.0f;
    
    // 每次调用增加2度
    demo->angle1 += direction1 * 2.0f;
    demo->angle2 += direction2 * 2.0f;
    
    // 检查边界并反向
    if(demo->angle1 >= 180.0f) {
        demo->angle1 = 180.0f;
        direction1 = -1.0f;
    } else if(demo->angle1 <= 0.0f) {
        demo->angle1 = 0.0f;
        direction1 = 1.0f;
    }
    
    if(demo->angle2 >= 180.0f) {
        demo->angle2 = 180.0f;
        direction2 = -1.0f;
    } else if(demo->angle2 <= 0.0f) {
        demo->angle2 = 0.0f;
        direction2 = 1.0f;
    }
}

// 步进模式：舵机在几个固定位置之间切换
void ServoDemo_StepMode(ServoDemo_t* demo)
{
    const float positions[] = {0.0f, 45.0f, 90.0f, 135.0f, 180.0f};
    const uint8_t num_positions = sizeof(positions) / sizeof(positions[0]);
    
    demo->delay_counter++;
    
    // 每50次调用切换一次位置（约0.5秒）
    if(demo->delay_counter >= 50) {
        demo->delay_counter = 0;
        demo->step_counter++;
        
        if(demo->step_counter >= num_positions) {
            demo->step_counter = 0;
        }
        
        demo->angle1 = positions[demo->step_counter];
        demo->angle2 = positions[(num_positions - 1) - demo->step_counter];
    }
}

// 随机模式：舵机随机移动到不同位置
void ServoDemo_RandomMode(ServoDemo_t* demo)
{
    demo->delay_counter++;
    
    // 每100次调用生成新的随机位置（约1秒）
    if(demo->delay_counter >= 100) {
        demo->delay_counter = 0;
        
        // 生成0-180度的随机角度
        demo->target_angle1 = (float)(rand() % 181);
        demo->target_angle2 = (float)(rand() % 181);
    }
    
    // 平滑移动到目标位置
    float diff1 = demo->target_angle1 - demo->angle1;
    float diff2 = demo->target_angle2 - demo->angle2;
    
    if(fabs(diff1) > 1.0f) {
        demo->angle1 += (diff1 > 0) ? 1.0f : -1.0f;
    }
    
    if(fabs(diff2) > 1.0f) {
        demo->angle2 += (diff2 > 0) ? 1.0f : -1.0f;
    }
}
