# MG996R舵机适配说明

## 概述
本文档说明如何将原轮趣电机控制代码适配为MG996R舵机控制系统。

## MG996R舵机规格
- **工作电压**: 4.8V - 6V
- **扭矩**: 9.4 kg·cm (4.8V), 11 kg·cm (6V)
- **转动角度**: 0° - 180°
- **控制信号**: 50Hz PWM
- **脉宽范围**: 1ms - 2ms
  - 1ms → 0°
  - 1.5ms → 90°
  - 2ms → 180°

## 硬件连接
```
MG996R舵机引脚连接：
- 棕色线(GND) → 开发板GND
- 红色线(VCC) → 5V电源
- 橙色线(信号) → PWM输出引脚

当前PWM引脚分配：
- PWM_0: PB17 (舵机1)
- PWM_1: PB16 (舵机2)
```

## 代码修改说明

### 1. PWM参数计算
原系统PWM配置：
- 频率: 50Hz ✓
- 周期: 20ms ✓
- 计数值: 10000

MG996R所需PWM值：
```c
#define SERVO_PWM_MIN    500    // 1ms  → 0°
#define SERVO_PWM_CENTER 750    // 1.5ms → 90°
#define SERVO_PWM_MAX    1000   // 2ms  → 180°
```

### 2. 新增函数

#### `Angle_to_PWM_MG996R(float angle)`
- 功能: 将角度(0-180°)转换为PWM值
- 参数: angle - 目标角度
- 返回: 对应的PWM值

#### `Set_Servo_Angle(float angle1, float angle2)`
- 功能: 设置两个舵机的角度
- 参数: angle1, angle2 - 两个舵机的目标角度

### 3. 主程序修改
- 将原来的PWM值控制改为角度控制
- 按键功能调整：
  - 单击: 角度+10°
  - 双击: 角度-10°
  - 长按: 回到90°中位

## 使用示例

### 基本控制
```c
// 设置舵机到指定角度
Set_Servo_Angle(45.0f, 135.0f);  // 舵机1→45°, 舵机2→135°

// 单独计算PWM值
int pwm_value = Angle_to_PWM_MG996R(90.0f);  // 90°对应PWM值
Set_PWM(pwm_value, pwm_value);
```

### 演示程序
提供了完整的演示程序 `mg996r_example.c`，包含：
1. **手动控制模式**: 通过按键调整角度
2. **扫描模式**: 0-180°连续扫描
3. **步进模式**: 固定位置切换
4. **随机模式**: 随机位置移动

## 文件清单

### 修改的文件
- `Hardware/motor.h` - 添加MG996R相关定义和函数声明
- `Hardware/motor.c` - 实现角度转换和控制函数
- `empty.c` - 修改主程序逻辑

### 新增的文件
- `Hardware/servo_demo.h` - 舵机演示功能头文件
- `Hardware/servo_demo.c` - 舵机演示功能实现
- `mg996r_example.c` - 完整的MG996R控制示例
- `MG996R_适配说明.md` - 本说明文档

## 编译和使用

### 编译选项
1. **使用修改后的原程序**: 直接编译现有项目
2. **使用完整示例**: 将 `mg996r_example.c` 替换 `empty.c`

### 测试步骤
1. 连接MG996R舵机到指定引脚
2. 上电，舵机应移动到90°中位
3. 按键测试：
   - 单击: 观察角度增加
   - 双击: 观察角度减少
   - 长按: 舵机回到中位

## 注意事项

### 电源要求
- MG996R需要5V电源，电流可达2.5A
- 确保电源能力足够，建议使用外部5V电源
- 开发板3.3V无法直接驱动

### 安全提示
- 首次使用时建议从小角度开始测试
- 确认舵机机械限位，避免过度转动
- 注意舵机发热，长时间使用需散热

### 调试建议
- 使用示波器检查PWM信号
- 串口输出可监控当前角度状态
- OLED显示提供实时反馈

## 扩展功能

### 可添加的功能
1. **PID位置控制**: 实现精确位置控制
2. **速度控制**: 控制舵机转动速度
3. **轨迹规划**: 实现复杂运动轨迹
4. **多舵机协调**: 控制更多舵机同步运动

### 应用场景
- 机器人关节控制
- 云台控制系统
- 自动化设备
- 教学演示平台
