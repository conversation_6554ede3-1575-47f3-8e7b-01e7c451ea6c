******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 21:08:32 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000129d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000018c8  0001e738  R  X
  SRAM                  20200000   00008000  00000837  000077c9  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000018c8   000018c8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001738   00001738    r-x .text
  000017f8    000017f8    00000098   00000098    r-- .rodata
  00001890    00001890    00000038   00000038    r-- .cinit
20200000    20200000    0000063a   00000000    rw-
  20200000    20200000    00000625   00000000    rw- .bss
  20200628    20200628    00000012   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001738     
                  000000c0    00000184     bsp_key.o (.text.key_scan)
                  00000244    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000032c    000000e4     oled.o (.text.OLED_Init)
                  00000410    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000004ec    000000d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000005bc    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00000680    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000720    00000090     board.o (.text.delay_us)
                  000007b0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000083c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000008c0    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00000942    00000002     --HOLE-- [fill = 0]
                  00000944    00000080     driverlib.a : dl_timer.o (.text.DL_TimerA_initPWMMode)
                  000009c4    0000007c     oled.o (.text.OLED_Refresh_Gram)
                  00000a40    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000abc    00000074     empty.o (.text.TIMA0_IRQHandler)
                  00000b30    0000006e     oled.o (.text.OLED_WR_Byte)
                  00000b9e    00000002     --HOLE-- [fill = 0]
                  00000ba0    0000006c     empty.o (.text.main)
                  00000c0c    00000060     oled.o (.text.OLED_Clear)
                  00000c6c    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_1_init)
                  00000cc8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00000d20    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000d78    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00000dcc    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_2_init)
                  00000e20    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00000e6c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00000eb8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000f00    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00000f48    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00000f8c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000fcc    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00001008    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001044    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000107e    00000002     --HOLE-- [fill = 0]
                  00001080    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000010b8    00000034     board.o (.text.UART3_IRQHandler)
                  000010ec    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  0000111c    00000030     board.o (.text.delay_ms)
                  0000114c    0000002c     board.o (.text.UART0_IRQHandler)
                  00001178    0000002c     board.o (.text.__NVIC_ClearPendingIRQ)
                  000011a4    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000011d0    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000011fc    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001224    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  0000124c    00000028     motor.o (.text.Set_PWM)
                  00001274    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0000129c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000012c4    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000012e8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001308    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00001328    00000020     bsp_key.o (.text.keyValue)
                  00001348    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00001366    00000002     --HOLE-- [fill = 0]
                  00001368    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInput)
                  00001384    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000013a0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000013bc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000013d8    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000013f4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001410    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000142c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00001448    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001460    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001478    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001490    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000014a8    00000018     oled.o (.text.DL_GPIO_setPins)
                  000014c0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000014d8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000014f0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001508    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001520    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001538    00000018     empty.o (.text.DL_Timer_startCounter)
                  00001550    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001568    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001580    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00001598    00000016     bsp_key.o (.text.DL_GPIO_readPins)
                  000015ae    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000015c4    00000014     oled.o (.text.DL_GPIO_clearPins)
                  000015d8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000015ec    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001600    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00001614    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00001628    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000163c    00000014     board.o (.text.DL_UART_receiveData)
                  00001650    00000014     oled.o (.text.OLED_RST_Clr)
                  00001664    00000014     oled.o (.text.OLED_RST_Set)
                  00001678    00000014     oled.o (.text.OLED_RS_Clr)
                  0000168c    00000014     oled.o (.text.OLED_RS_Set)
                  000016a0    00000014     oled.o (.text.OLED_SCLK_Clr)
                  000016b4    00000014     oled.o (.text.OLED_SCLK_Set)
                  000016c8    00000014     oled.o (.text.OLED_SDIN_Clr)
                  000016dc    00000014     oled.o (.text.OLED_SDIN_Set)
                  000016f0    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00001702    00000012     board.o (.text.DL_UART_getPendingInterrupt)
                  00001714    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00001726    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00001738    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000174a    00000002     --HOLE-- [fill = 0]
                  0000174c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000175c    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  0000176c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000177c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000178c    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  0000179c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000017aa    00000002     --HOLE-- [fill = 0]
                  000017ac    0000000c     board.o (.text.Systick_getTick)
                  000017b8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000017c4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000017ce    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000017d6    00000002     --HOLE-- [fill = 0]
                  000017d8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000017e0    00000006     libc.a : exit.c.obj (.text:abort)
                  000017e6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000017ea    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000017ee    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000017f2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000017f6    00000002     --HOLE-- [fill = 0]

.cinit     0    00001890    00000038     
                  00001890    00000011     (.cinit..data.load) [load image, compression = lzss]
                  000018a1    00000003     --HOLE-- [fill = 0]
                  000018a4    0000000c     (__TI_handler_table)
                  000018b0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000018b8    00000010     (__TI_cinit_table)

.rodata    0    000017f8    00000098     
                  000017f8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001820    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00001838    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  0000184c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00001856    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00001860    0000000a     ti_msp_dl_config.o (.rodata.gUART_2Config)
                  0000186a    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  0000186c    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00001874    00000008     ti_msp_dl_config.o (.rodata.gPWM_1Config)
                  0000187c    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  0000187f    00000003     ti_msp_dl_config.o (.rodata.gPWM_1ClockConfig)
                  00001882    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00001885    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00001887    00000002     ti_msp_dl_config.o (.rodata.gUART_2ClockConfig)
                  00001889    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000625     UNINITIALIZED
                  20200000    00000400     (.common:OLED_GRAM)
                  20200400    000000bc     (.common:gPWM_0Backup)
                  202004bc    000000bc     (.common:gTIMER_0Backup)
                  20200578    00000078     (.common:gPWM_1Backup)
                  202005f0    00000030     (.common:gUART_2Backup)
                  20200620    00000002     bsp_key.o (.bss.key_scan.long_press_time)
                  20200622    00000002     bsp_key.o (.bss.key_scan.time_core)
                  20200624    00000001     (.common:keystate)

.data      0    20200628    00000012     UNINITIALIZED
                  20200628    00000004     bsp_key.o (.data.UserKey)
                  2020062c    00000004     empty.o (.data.a)
                  20200630    00000004     empty.o (.data.key)
                  20200634    00000004     empty.o (.data.pwm)
                  20200638    00000001     bsp_key.o (.data.key_scan.check_once)
                  20200639    00000001     bsp_key.o (.data.key_scan.press_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2076   145       544    
       empty.o                        354    0         13     
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         2438   337       557    
                                                              
    .\Hardware\
       oled.o                         762    0         1024   
       bsp_key.o                      442    0         10     
       board.o                        382    0         0      
       motor.o                        40     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1626   0         1034   
                                                              
    D:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     652    0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288    0         0      
       dl_uart.o                      90     0         0      
       dl_dma.o                       76     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1116   0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       copy_zero_init.c.obj           16     0         0      
       memset16.S.obj                 14     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         300    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memset.S.obj             12     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         444    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      53        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   5928   390       2103   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000018b8 records: 2, size/record: 8, table size: 16
	.data: load addr=00001890, load size=00000011 bytes, run addr=20200628, run size=00000012 bytes, compression=lzss
	.bss: load addr=000018b0, load size=00000008 bytes, run addr=20200000, run size=00000625 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000018a4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000017e7  ADC0_IRQHandler                      
000017e7  ADC1_IRQHandler                      
000017e7  AES_IRQHandler                       
000017ea  C$$EXIT                              
000017e7  CANFD0_IRQHandler                    
000017e7  DAC0_IRQHandler                      
000017c5  DL_Common_delayCycles                
00000e21  DL_DMA_initChannel                   
00000411  DL_SYSCTL_configSYSPLL               
00000f49  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000945  DL_TimerA_initPWMMode                
000005bd  DL_Timer_initPWMMode                 
00000245  DL_Timer_initTimerMode               
000013f5  DL_Timer_setCaptCompUpdateMethod     
00001521  DL_Timer_setCaptureCompareOutCtl     
0000176d  DL_Timer_setCaptureCompareValue      
00001411  DL_Timer_setClockConfig              
00000eb9  DL_UART_init                         
00001715  DL_UART_setClockConfig               
000017e7  DMA_IRQHandler                       
000017e7  Default_Handler                      
000017e7  GROUP0_IRQHandler                    
000017e7  GROUP1_IRQHandler                    
000017eb  HOSTexit                             
000017e7  HardFault_Handler                    
000017e7  I2C0_IRQHandler                      
000017e7  I2C1_IRQHandler                      
000017e7  NMI_Handler                          
00000c0d  OLED_Clear                           
20200000  OLED_GRAM                            
0000032d  OLED_Init                            
00001651  OLED_RST_Clr                         
00001665  OLED_RST_Set                         
00001679  OLED_RS_Clr                          
0000168d  OLED_RS_Set                          
000009c5  OLED_Refresh_Gram                    
000016a1  OLED_SCLK_Clr                        
000016b5  OLED_SCLK_Set                        
000016c9  OLED_SDIN_Clr                        
000016dd  OLED_SDIN_Set                        
00000b31  OLED_WR_Byte                         
000017e7  PendSV_Handler                       
000017e7  RTC_IRQHandler                       
000017ef  Reset_Handler                        
000017e7  SPI0_IRQHandler                      
000017e7  SPI1_IRQHandler                      
000017e7  SVC_Handler                          
00001581  SYSCFG_DL_DMA_CH0_init               
000017cf  SYSCFG_DL_DMA_init                   
000004ed  SYSCFG_DL_GPIO_init                  
00000cc9  SYSCFG_DL_PWM_0_init                 
00000c6d  SYSCFG_DL_PWM_1_init                 
00000f8d  SYSCFG_DL_SYSCTL_init                
0000177d  SYSCFG_DL_SYSTICK_init               
00000fcd  SYSCFG_DL_TIMER_0_init               
00000f01  SYSCFG_DL_UART_0_init                
00000d79  SYSCFG_DL_UART_1_init                
00000dcd  SYSCFG_DL_UART_2_init                
00000d21  SYSCFG_DL_init                       
00000681  SYSCFG_DL_initPower                  
0000124d  Set_PWM                              
000017e7  SysTick_Handler                      
000017ad  Systick_getTick                      
00000abd  TIMA0_IRQHandler                     
000017e7  TIMA1_IRQHandler                     
000017e7  TIMG0_IRQHandler                     
000017e7  TIMG12_IRQHandler                    
000017e7  TIMG6_IRQHandler                     
000017e7  TIMG7_IRQHandler                     
000017e7  TIMG8_IRQHandler                     
00001727  TI_memcpy_small                      
0000179d  TI_memset_small                      
0000114d  UART0_IRQHandler                     
000017e7  UART1_IRQHandler                     
000017e7  UART2_IRQHandler                     
000010b9  UART3_IRQHandler                     
20200628  UserKey                              
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000018b8  __TI_CINIT_Base                      
000018c8  __TI_CINIT_Limit                     
000018c8  __TI_CINIT_Warm                      
000018a4  __TI_Handler_Table_Base              
000018b0  __TI_Handler_Table_Limit             
00001009  __TI_auto_init_nobinit_nopinit       
00000a41  __TI_decompress_lzss                 
00001739  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000178d  __TI_zero_init                       
00001081  __aeabi_f2iz                         
000008c1  __aeabi_fdiv                         
000007b1  __aeabi_fmul                         
000017b9  __aeabi_memclr                       
000017b9  __aeabi_memclr4                      
000017b9  __aeabi_memclr8                      
000017d9  __aeabi_memcpy                       
000017d9  __aeabi_memcpy4                      
000017d9  __aeabi_memcpy8                      
00001275  __aeabi_ui2f                         
ffffffff  __binit__                            
000008c1  __divsf3                             
00001081  __fixsfsi                            
00001275  __floatunsisf                        
UNDEFED   __mpu_init                           
00001045  __muldsi3                            
000007b1  __mulsf3                             
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000129d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000017f3  _system_pre_init                     
2020062c  a                                    
000017e1  abort                                
ffffffff  binit                                
0000111d  delay_ms                             
00000721  delay_us                             
20200400  gPWM_0Backup                         
20200578  gPWM_1Backup                         
202004bc  gTIMER_0Backup                       
202005f0  gUART_2Backup                        
00000000  interruptVectors                     
20200630  key                                  
20200624  keystate                             
00000ba1  main                                 
20200634  pwm                                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
00000200  __STACK_SIZE                         
00000245  DL_Timer_initTimerMode               
0000032d  OLED_Init                            
00000411  DL_SYSCTL_configSYSPLL               
000004ed  SYSCFG_DL_GPIO_init                  
000005bd  DL_Timer_initPWMMode                 
00000681  SYSCFG_DL_initPower                  
00000721  delay_us                             
000007b1  __aeabi_fmul                         
000007b1  __mulsf3                             
000008c1  __aeabi_fdiv                         
000008c1  __divsf3                             
00000945  DL_TimerA_initPWMMode                
000009c5  OLED_Refresh_Gram                    
00000a41  __TI_decompress_lzss                 
00000abd  TIMA0_IRQHandler                     
00000b31  OLED_WR_Byte                         
00000ba1  main                                 
00000c0d  OLED_Clear                           
00000c6d  SYSCFG_DL_PWM_1_init                 
00000cc9  SYSCFG_DL_PWM_0_init                 
00000d21  SYSCFG_DL_init                       
00000d79  SYSCFG_DL_UART_1_init                
00000dcd  SYSCFG_DL_UART_2_init                
00000e21  DL_DMA_initChannel                   
00000eb9  DL_UART_init                         
00000f01  SYSCFG_DL_UART_0_init                
00000f49  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000f8d  SYSCFG_DL_SYSCTL_init                
00000fcd  SYSCFG_DL_TIMER_0_init               
00001009  __TI_auto_init_nobinit_nopinit       
00001045  __muldsi3                            
00001081  __aeabi_f2iz                         
00001081  __fixsfsi                            
000010b9  UART3_IRQHandler                     
0000111d  delay_ms                             
0000114d  UART0_IRQHandler                     
0000124d  Set_PWM                              
00001275  __aeabi_ui2f                         
00001275  __floatunsisf                        
0000129d  _c_int00_noargs                      
000013f5  DL_Timer_setCaptCompUpdateMethod     
00001411  DL_Timer_setClockConfig              
00001521  DL_Timer_setCaptureCompareOutCtl     
00001581  SYSCFG_DL_DMA_CH0_init               
00001651  OLED_RST_Clr                         
00001665  OLED_RST_Set                         
00001679  OLED_RS_Clr                          
0000168d  OLED_RS_Set                          
000016a1  OLED_SCLK_Clr                        
000016b5  OLED_SCLK_Set                        
000016c9  OLED_SDIN_Clr                        
000016dd  OLED_SDIN_Set                        
00001715  DL_UART_setClockConfig               
00001727  TI_memcpy_small                      
00001739  __TI_decompress_none                 
0000176d  DL_Timer_setCaptureCompareValue      
0000177d  SYSCFG_DL_SYSTICK_init               
0000178d  __TI_zero_init                       
0000179d  TI_memset_small                      
000017ad  Systick_getTick                      
000017b9  __aeabi_memclr                       
000017b9  __aeabi_memclr4                      
000017b9  __aeabi_memclr8                      
000017c5  DL_Common_delayCycles                
000017cf  SYSCFG_DL_DMA_init                   
000017d9  __aeabi_memcpy                       
000017d9  __aeabi_memcpy4                      
000017d9  __aeabi_memcpy8                      
000017e1  abort                                
000017e7  ADC0_IRQHandler                      
000017e7  ADC1_IRQHandler                      
000017e7  AES_IRQHandler                       
000017e7  CANFD0_IRQHandler                    
000017e7  DAC0_IRQHandler                      
000017e7  DMA_IRQHandler                       
000017e7  Default_Handler                      
000017e7  GROUP0_IRQHandler                    
000017e7  GROUP1_IRQHandler                    
000017e7  HardFault_Handler                    
000017e7  I2C0_IRQHandler                      
000017e7  I2C1_IRQHandler                      
000017e7  NMI_Handler                          
000017e7  PendSV_Handler                       
000017e7  RTC_IRQHandler                       
000017e7  SPI0_IRQHandler                      
000017e7  SPI1_IRQHandler                      
000017e7  SVC_Handler                          
000017e7  SysTick_Handler                      
000017e7  TIMA1_IRQHandler                     
000017e7  TIMG0_IRQHandler                     
000017e7  TIMG12_IRQHandler                    
000017e7  TIMG6_IRQHandler                     
000017e7  TIMG7_IRQHandler                     
000017e7  TIMG8_IRQHandler                     
000017e7  UART1_IRQHandler                     
000017e7  UART2_IRQHandler                     
000017ea  C$$EXIT                              
000017eb  HOSTexit                             
000017ef  Reset_Handler                        
000017f3  _system_pre_init                     
000018a4  __TI_Handler_Table_Base              
000018b0  __TI_Handler_Table_Limit             
000018b8  __TI_CINIT_Base                      
000018c8  __TI_CINIT_Limit                     
000018c8  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200400  gPWM_0Backup                         
202004bc  gTIMER_0Backup                       
20200578  gPWM_1Backup                         
202005f0  gUART_2Backup                        
20200624  keystate                             
20200628  UserKey                              
2020062c  a                                    
20200630  key                                  
20200634  pwm                                  
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[140 symbols]
