{"version": "0.2.0", "configurations": [{"name": "Debug MSPM0G3507", "type": "cortex-debug", "request": "launch", "servertype": "openocd", "cwd": "${workspaceRoot}", "executable": "./Debug/empty_LP_MSPM0G3507_nortos_ticlang.out", "device": "MSPM0G3507", "configFiles": ["interface/xds110.cfg", "target/ti_mspm0g3507.cfg"], "svdFile": "${workspaceRoot}/MSPM0G3507.svd", "runToMain": true, "showDevDebugOutput": true}]}