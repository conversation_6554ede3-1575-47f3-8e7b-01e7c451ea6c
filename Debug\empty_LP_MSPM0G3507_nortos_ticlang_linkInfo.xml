<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iD:/ti/mspm0_sdk_2_01_00_03/source -iC:/Users/<USER>/Desktop/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/Desktop/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./Hardware/board.o ./Hardware/bsp_key.o ./Hardware/motor.o ./Hardware/oled.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688a1950</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x129d</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>board.o</file>
         <name>board.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>bsp_key.o</file>
         <name>bsp_key.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>oled.o</file>
         <name>oled.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-e2">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-e3">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-e4">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-e5">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-e6">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-e7">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-e8">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-e9">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-ea">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.key_scan</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x184</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x244</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x244</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.text.OLED_Init</name>
         <load_address>0x32c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x32c</run_address>
         <size>0xe4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x410</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x410</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x4ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4ec</run_address>
         <size>0xd0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.DL_Timer_initPWMMode</name>
         <load_address>0x5bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5bc</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x680</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x680</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.delay_us</name>
         <load_address>0x720</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x720</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.__mulsf3</name>
         <load_address>0x7b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7b0</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x83c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x83c</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.__divsf3</name>
         <load_address>0x8c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8c0</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.DL_TimerA_initPWMMode</name>
         <load_address>0x944</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x944</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.OLED_Refresh_Gram</name>
         <load_address>0x9c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9c4</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0xa40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa40</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.text.TIMA0_IRQHandler</name>
         <load_address>0xabc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xabc</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0xb30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb30</run_address>
         <size>0x6e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.text.main</name>
         <load_address>0xba0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xba0</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.OLED_Clear</name>
         <load_address>0xc0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0c</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.SYSCFG_DL_PWM_1_init</name>
         <load_address>0xc6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc6c</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.SYSCFG_DL_PWM_0_init</name>
         <load_address>0xcc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcc8</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xd20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd20</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.SYSCFG_DL_UART_1_init</name>
         <load_address>0xd78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd78</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.SYSCFG_DL_UART_2_init</name>
         <load_address>0xdcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdcc</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0xe20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe20</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0xe6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe6c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_UART_init</name>
         <load_address>0xeb8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeb8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0xf00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf00</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-130">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0xf48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf48</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xf8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf8c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0xfcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfcc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1008</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1008</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.text.__muldsi3</name>
         <load_address>0x1044</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1044</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.__fixsfsi</name>
         <load_address>0x1080</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1080</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.UART3_IRQHandler</name>
         <load_address>0x10b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10b8</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.DL_GPIO_initDigitalOutputFeatures</name>
         <load_address>0x10ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10ec</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.text.delay_ms</name>
         <load_address>0x111c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x111c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x114c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x114c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-62">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x1178</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1178</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x11a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11a4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x11d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11d0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-179">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x11fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11fc</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-155">
         <name>.text.DL_SYSTICK_init</name>
         <load_address>0x1224</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1224</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-67">
         <name>.text.Set_PWM</name>
         <load_address>0x124c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x124c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.__floatunsisf</name>
         <load_address>0x1274</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1274</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x129c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x129c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.text.DL_UART_setRXInterruptTimeout</name>
         <load_address>0x12c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12c4</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x12e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12e8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.DL_SYSCTL_setFlashWaitState</name>
         <load_address>0x1308</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1308</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.keyValue</name>
         <load_address>0x1328</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1328</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x1348</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1348</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.text.DL_GPIO_initDigitalInput</name>
         <load_address>0x1368</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1368</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1384</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1384</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-125">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x13a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13a0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x13bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13bc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x13d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13d8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x13f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13f4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x1410</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1410</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x142c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x142c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1448</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1448</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1460</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1460</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1478</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1478</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-111">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1490</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1490</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-158">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x14a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14a8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x14c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14c0</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-123">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x14d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14d8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x14f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14f0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1508</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1508</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1520</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1520</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x1538</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1538</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1550</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1550</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1568</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1568</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-154">
         <name>.text.SYSCFG_DL_DMA_CH0_init</name>
         <load_address>0x1580</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1580</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-174">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1598</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1598</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_UART_enable</name>
         <load_address>0x15ae</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15ae</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-157">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x15c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15c4</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x15d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15d8</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x15ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15ec</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.DL_SYSCTL_enableMFCLK</name>
         <load_address>0x1600</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1600</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-136">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x1614</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1614</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-137">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x1628</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1628</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x163c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x163c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.OLED_RST_Clr</name>
         <load_address>0x1650</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1650</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.OLED_RST_Set</name>
         <load_address>0x1664</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1664</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.text.OLED_RS_Clr</name>
         <load_address>0x1678</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1678</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-159">
         <name>.text.OLED_RS_Set</name>
         <load_address>0x168c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x168c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.text.OLED_SCLK_Clr</name>
         <load_address>0x16a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16a0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.text.OLED_SCLK_Set</name>
         <load_address>0x16b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16b4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.text.OLED_SDIN_Clr</name>
         <load_address>0x16c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16c8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.text.OLED_SDIN_Set</name>
         <load_address>0x16dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16dc</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-66">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x16f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16f0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-61">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x1702</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1702</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-142">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x1714</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1714</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x1726</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1726</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x1738</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1738</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x174c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x174c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-156">
         <name>.text.DL_SYSTICK_enable</name>
         <load_address>0x175c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x175c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x176c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x176c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x177c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x177c</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-57">
         <name>.text:decompress:ZI</name>
         <load_address>0x178c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x178c</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text:TI_memset_small</name>
         <load_address>0x179c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x179c</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-161">
         <name>.text.Systick_getTick</name>
         <load_address>0x17ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17ac</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-74">
         <name>.text.__aeabi_memclr</name>
         <load_address>0x17b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17b8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x17c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17c4</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0x17ce</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17ce</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x17d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17d8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text:abort</name>
         <load_address>0x17e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17e0</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x17e6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17e6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.HOSTexit</name>
         <load_address>0x17ea</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17ea</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x17ee</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17ee</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text._system_pre_init</name>
         <load_address>0x17f2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17f2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.cinit..data.load</name>
         <load_address>0x1890</load_address>
         <readonly>true</readonly>
         <run_address>0x1890</run_address>
         <size>0x11</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1c6">
         <name>__TI_handler_table</name>
         <load_address>0x18a4</load_address>
         <readonly>true</readonly>
         <run_address>0x18a4</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1c9">
         <name>.cinit..bss.load</name>
         <load_address>0x18b0</load_address>
         <readonly>true</readonly>
         <run_address>0x18b0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1c7">
         <name>__TI_cinit_table</name>
         <load_address>0x18b8</load_address>
         <readonly>true</readonly>
         <run_address>0x18b8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-131">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x17f8</load_address>
         <readonly>true</readonly>
         <run_address>0x17f8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-182">
         <name>.rodata.gDMA_CH0Config</name>
         <load_address>0x1820</load_address>
         <readonly>true</readonly>
         <run_address>0x1820</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-141">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x1838</load_address>
         <readonly>true</readonly>
         <run_address>0x1838</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x184c</load_address>
         <readonly>true</readonly>
         <run_address>0x184c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.rodata.gUART_1Config</name>
         <load_address>0x1856</load_address>
         <readonly>true</readonly>
         <run_address>0x1856</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-153">
         <name>.rodata.gUART_2Config</name>
         <load_address>0x1860</load_address>
         <readonly>true</readonly>
         <run_address>0x1860</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x186a</load_address>
         <readonly>true</readonly>
         <run_address>0x186a</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-139">
         <name>.rodata.gPWM_0Config</name>
         <load_address>0x186c</load_address>
         <readonly>true</readonly>
         <run_address>0x186c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.rodata.gPWM_1Config</name>
         <load_address>0x1874</load_address>
         <readonly>true</readonly>
         <run_address>0x1874</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-138">
         <name>.rodata.gPWM_0ClockConfig</name>
         <load_address>0x187c</load_address>
         <readonly>true</readonly>
         <run_address>0x187c</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.rodata.gPWM_1ClockConfig</name>
         <load_address>0x187f</load_address>
         <readonly>true</readonly>
         <run_address>0x187f</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-140">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x1882</load_address>
         <readonly>true</readonly>
         <run_address>0x1882</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-150">
         <name>.rodata.gUART_1ClockConfig</name>
         <load_address>0x1885</load_address>
         <readonly>true</readonly>
         <run_address>0x1885</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-152">
         <name>.rodata.gUART_2ClockConfig</name>
         <load_address>0x1887</load_address>
         <readonly>true</readonly>
         <run_address>0x1887</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-190">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-aa">
         <name>.data.a</name>
         <load_address>0x2020062c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020062c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.data.pwm</name>
         <load_address>0x20200634</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200634</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.data.key</name>
         <load_address>0x20200630</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200630</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-90">
         <name>.data.UserKey</name>
         <load_address>0x20200628</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200628</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.data.key_scan.press_flag</name>
         <load_address>0x20200639</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200639</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.data.key_scan.check_once</name>
         <load_address>0x20200638</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200638</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.bss.key_scan.time_core</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200622</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.bss.key_scan.long_press_time</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200620</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.common:keystate</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200624</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-de">
         <name>.common:gPWM_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200400</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-df">
         <name>.common:gPWM_1Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200578</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e0">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202004bc</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e1">
         <name>.common:gUART_2Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202005f0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-160">
         <name>.common:OLED_GRAM</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x400</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_abbrev</name>
         <load_address>0x1a6</load_address>
         <run_address>0x1a6</run_address>
         <size>0x226</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_abbrev</name>
         <load_address>0x3cc</load_address>
         <run_address>0x3cc</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_abbrev</name>
         <load_address>0x439</load_address>
         <run_address>0x439</run_address>
         <size>0x1d3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_abbrev</name>
         <load_address>0x60c</load_address>
         <run_address>0x60c</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_abbrev</name>
         <load_address>0x754</load_address>
         <run_address>0x754</run_address>
         <size>0xe2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_abbrev</name>
         <load_address>0x836</load_address>
         <run_address>0x836</run_address>
         <size>0x17b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_abbrev</name>
         <load_address>0x9b1</load_address>
         <run_address>0x9b1</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-184">
         <name>.debug_abbrev</name>
         <load_address>0xa13</load_address>
         <run_address>0xa13</run_address>
         <size>0x182</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_abbrev</name>
         <load_address>0xb95</load_address>
         <run_address>0xb95</run_address>
         <size>0x258</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.debug_abbrev</name>
         <load_address>0xded</load_address>
         <run_address>0xded</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.debug_abbrev</name>
         <load_address>0x106c</load_address>
         <run_address>0x106c</run_address>
         <size>0x259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0x12c5</load_address>
         <run_address>0x12c5</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_abbrev</name>
         <load_address>0x1374</load_address>
         <run_address>0x1374</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_abbrev</name>
         <load_address>0x14e4</load_address>
         <run_address>0x14e4</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_abbrev</name>
         <load_address>0x151d</load_address>
         <run_address>0x151d</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_abbrev</name>
         <load_address>0x15df</load_address>
         <run_address>0x15df</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_abbrev</name>
         <load_address>0x164f</load_address>
         <run_address>0x164f</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_abbrev</name>
         <load_address>0x16dc</load_address>
         <run_address>0x16dc</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_abbrev</name>
         <load_address>0x1774</load_address>
         <run_address>0x1774</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_abbrev</name>
         <load_address>0x17a0</load_address>
         <run_address>0x17a0</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_abbrev</name>
         <load_address>0x17c7</load_address>
         <run_address>0x17c7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_abbrev</name>
         <load_address>0x17ee</load_address>
         <run_address>0x17ee</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_abbrev</name>
         <load_address>0x1815</load_address>
         <run_address>0x1815</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_abbrev</name>
         <load_address>0x183c</load_address>
         <run_address>0x183c</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_abbrev</name>
         <load_address>0x1863</load_address>
         <run_address>0x1863</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-183">
         <name>.debug_abbrev</name>
         <load_address>0x1888</load_address>
         <run_address>0x1888</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0x18af</load_address>
         <run_address>0x18af</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_abbrev</name>
         <load_address>0x18d4</load_address>
         <run_address>0x18d4</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.debug_abbrev</name>
         <load_address>0x18f9</load_address>
         <run_address>0x18f9</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb15</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_info</name>
         <load_address>0xb15</load_address>
         <run_address>0xb15</run_address>
         <size>0x4734</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x5249</load_address>
         <run_address>0x5249</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x52c9</load_address>
         <run_address>0x52c9</run_address>
         <size>0xb8f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_info</name>
         <load_address>0x5e58</load_address>
         <run_address>0x5e58</run_address>
         <size>0x849</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_info</name>
         <load_address>0x66a1</load_address>
         <run_address>0x66a1</run_address>
         <size>0x6f7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_info</name>
         <load_address>0x6d98</load_address>
         <run_address>0x6d98</run_address>
         <size>0x1019</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_info</name>
         <load_address>0x7db1</load_address>
         <run_address>0x7db1</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_info</name>
         <load_address>0x7e26</load_address>
         <run_address>0x7e26</run_address>
         <size>0x6df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_info</name>
         <load_address>0x8505</load_address>
         <run_address>0x8505</run_address>
         <size>0x2f7d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_info</name>
         <load_address>0xb482</load_address>
         <run_address>0xb482</run_address>
         <size>0x1259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_info</name>
         <load_address>0xc6db</load_address>
         <run_address>0xc6db</run_address>
         <size>0x1f76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xe651</load_address>
         <run_address>0xe651</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_info</name>
         <load_address>0xea74</load_address>
         <run_address>0xea74</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_info</name>
         <load_address>0xf1b8</load_address>
         <run_address>0xf1b8</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_info</name>
         <load_address>0xf1fe</load_address>
         <run_address>0xf1fe</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0xf390</load_address>
         <run_address>0xf390</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xf456</load_address>
         <run_address>0xf456</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_info</name>
         <load_address>0xf5d2</load_address>
         <run_address>0xf5d2</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_info</name>
         <load_address>0xf6ca</load_address>
         <run_address>0xf6ca</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_info</name>
         <load_address>0xf705</load_address>
         <run_address>0xf705</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_info</name>
         <load_address>0xf892</load_address>
         <run_address>0xf892</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_info</name>
         <load_address>0xfa1f</load_address>
         <run_address>0xfa1f</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_info</name>
         <load_address>0xfbae</load_address>
         <run_address>0xfbae</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0xfd45</load_address>
         <run_address>0xfd45</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_info</name>
         <load_address>0xfede</load_address>
         <run_address>0xfede</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_info</name>
         <load_address>0x10093</load_address>
         <run_address>0x10093</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_info</name>
         <load_address>0x10222</load_address>
         <run_address>0x10222</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_info</name>
         <load_address>0x1051c</load_address>
         <run_address>0x1051c</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.debug_info</name>
         <load_address>0x10760</load_address>
         <run_address>0x10760</run_address>
         <size>0xb2</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_ranges</name>
         <load_address>0x38</load_address>
         <run_address>0x38</run_address>
         <size>0x188</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x1c0</load_address>
         <run_address>0x1c0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0x1d8</load_address>
         <run_address>0x1d8</run_address>
         <size>0x88</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_ranges</name>
         <load_address>0x260</load_address>
         <run_address>0x260</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_ranges</name>
         <load_address>0x280</load_address>
         <run_address>0x280</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_ranges</name>
         <load_address>0x350</load_address>
         <run_address>0x350</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_ranges</name>
         <load_address>0x4c0</load_address>
         <run_address>0x4c0</run_address>
         <size>0x190</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_ranges</name>
         <load_address>0x650</load_address>
         <run_address>0x650</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x7f8</load_address>
         <run_address>0x7f8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_ranges</name>
         <load_address>0x840</load_address>
         <run_address>0x840</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_ranges</name>
         <load_address>0x888</load_address>
         <run_address>0x888</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_ranges</name>
         <load_address>0x8a0</load_address>
         <run_address>0x8a0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_ranges</name>
         <load_address>0x8f0</load_address>
         <run_address>0x8f0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_ranges</name>
         <load_address>0x908</load_address>
         <run_address>0x908</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_ranges</name>
         <load_address>0x930</load_address>
         <run_address>0x930</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_ranges</name>
         <load_address>0x958</load_address>
         <run_address>0x958</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x901</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_str</name>
         <load_address>0x901</load_address>
         <run_address>0x901</run_address>
         <size>0x3432</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_str</name>
         <load_address>0x3d33</load_address>
         <run_address>0x3d33</run_address>
         <size>0x169</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_str</name>
         <load_address>0x3e9c</load_address>
         <run_address>0x3e9c</run_address>
         <size>0x92a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_str</name>
         <load_address>0x47c6</load_address>
         <run_address>0x47c6</run_address>
         <size>0x56d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_str</name>
         <load_address>0x4d33</load_address>
         <run_address>0x4d33</run_address>
         <size>0x451</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_str</name>
         <load_address>0x5184</load_address>
         <run_address>0x5184</run_address>
         <size>0x6a1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_str</name>
         <load_address>0x5825</load_address>
         <run_address>0x5825</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-185">
         <name>.debug_str</name>
         <load_address>0x599c</load_address>
         <run_address>0x599c</run_address>
         <size>0x686</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_str</name>
         <load_address>0x6022</load_address>
         <run_address>0x6022</run_address>
         <size>0x1c27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_str</name>
         <load_address>0x7c49</load_address>
         <run_address>0x7c49</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_str</name>
         <load_address>0x8936</load_address>
         <run_address>0x8936</run_address>
         <size>0x16bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x9ff2</load_address>
         <run_address>0x9ff2</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_str</name>
         <load_address>0xa217</load_address>
         <run_address>0xa217</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_str</name>
         <load_address>0xa546</load_address>
         <run_address>0xa546</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_str</name>
         <load_address>0xa63b</load_address>
         <run_address>0xa63b</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_str</name>
         <load_address>0xa7d6</load_address>
         <run_address>0xa7d6</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_str</name>
         <load_address>0xa93e</load_address>
         <run_address>0xa93e</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_str</name>
         <load_address>0xab13</load_address>
         <run_address>0xab13</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_str</name>
         <load_address>0xac5b</load_address>
         <run_address>0xac5b</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x94</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_frame</name>
         <load_address>0x94</load_address>
         <run_address>0x94</run_address>
         <size>0x46c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x500</load_address>
         <run_address>0x500</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x530</load_address>
         <run_address>0x530</run_address>
         <size>0x194</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_frame</name>
         <load_address>0x6c4</load_address>
         <run_address>0x6c4</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_frame</name>
         <load_address>0x720</load_address>
         <run_address>0x720</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_frame</name>
         <load_address>0x74c</load_address>
         <run_address>0x74c</run_address>
         <size>0x29c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_frame</name>
         <load_address>0x9e8</load_address>
         <run_address>0x9e8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_frame</name>
         <load_address>0xa08</load_address>
         <run_address>0xa08</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_frame</name>
         <load_address>0xa38</load_address>
         <run_address>0xa38</run_address>
         <size>0x400</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_frame</name>
         <load_address>0xe38</load_address>
         <run_address>0xe38</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_frame</name>
         <load_address>0xff0</load_address>
         <run_address>0xff0</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_frame</name>
         <load_address>0x111c</load_address>
         <run_address>0x111c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_frame</name>
         <load_address>0x11ac</load_address>
         <run_address>0x11ac</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_frame</name>
         <load_address>0x12ac</load_address>
         <run_address>0x12ac</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_frame</name>
         <load_address>0x12cc</load_address>
         <run_address>0x12cc</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x1304</load_address>
         <run_address>0x1304</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x132c</load_address>
         <run_address>0x132c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_frame</name>
         <load_address>0x135c</load_address>
         <run_address>0x135c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_frame</name>
         <load_address>0x138c</load_address>
         <run_address>0x138c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3c2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_line</name>
         <load_address>0x3c2</load_address>
         <run_address>0x3c2</run_address>
         <size>0xc33</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0xff5</load_address>
         <run_address>0xff5</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x10ad</load_address>
         <run_address>0x10ad</run_address>
         <size>0x5a9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_line</name>
         <load_address>0x1656</load_address>
         <run_address>0x1656</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_line</name>
         <load_address>0x1950</load_address>
         <run_address>0x1950</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_line</name>
         <load_address>0x1b28</load_address>
         <run_address>0x1b28</run_address>
         <size>0xaf1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_line</name>
         <load_address>0x2619</load_address>
         <run_address>0x2619</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_line</name>
         <load_address>0x26fd</load_address>
         <run_address>0x26fd</run_address>
         <size>0x1b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_line</name>
         <load_address>0x28ad</load_address>
         <run_address>0x28ad</run_address>
         <size>0x15a2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_line</name>
         <load_address>0x3e4f</load_address>
         <run_address>0x3e4f</run_address>
         <size>0x989</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_line</name>
         <load_address>0x47d8</load_address>
         <run_address>0x47d8</run_address>
         <size>0x8e4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_line</name>
         <load_address>0x50bc</load_address>
         <run_address>0x50bc</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_line</name>
         <load_address>0x5298</load_address>
         <run_address>0x5298</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_line</name>
         <load_address>0x57b2</load_address>
         <run_address>0x57b2</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_line</name>
         <load_address>0x57f0</load_address>
         <run_address>0x57f0</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x58ee</load_address>
         <run_address>0x58ee</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x59ae</load_address>
         <run_address>0x59ae</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_line</name>
         <load_address>0x5b76</load_address>
         <run_address>0x5b76</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_line</name>
         <load_address>0x5bdd</load_address>
         <run_address>0x5bdd</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_line</name>
         <load_address>0x5c1e</load_address>
         <run_address>0x5c1e</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_line</name>
         <load_address>0x5cfe</load_address>
         <run_address>0x5cfe</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_line</name>
         <load_address>0x5dda</load_address>
         <run_address>0x5dda</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_line</name>
         <load_address>0x5e92</load_address>
         <run_address>0x5e92</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_line</name>
         <load_address>0x5f46</load_address>
         <run_address>0x5f46</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_line</name>
         <load_address>0x5fea</load_address>
         <run_address>0x5fea</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_line</name>
         <load_address>0x60a4</load_address>
         <run_address>0x60a4</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_line</name>
         <load_address>0x615d</load_address>
         <run_address>0x615d</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_line</name>
         <load_address>0x61fd</load_address>
         <run_address>0x61fd</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-186">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_loc</name>
         <load_address>0xd0</load_address>
         <run_address>0xd0</run_address>
         <size>0x18ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_loc</name>
         <load_address>0x197d</load_address>
         <run_address>0x197d</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_loc</name>
         <load_address>0x2139</load_address>
         <run_address>0x2139</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_loc</name>
         <load_address>0x254d</load_address>
         <run_address>0x254d</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_loc</name>
         <load_address>0x2625</load_address>
         <run_address>0x2625</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_loc</name>
         <load_address>0x2a49</load_address>
         <run_address>0x2a49</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_loc</name>
         <load_address>0x2bb5</load_address>
         <run_address>0x2bb5</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_loc</name>
         <load_address>0x2c24</load_address>
         <run_address>0x2c24</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_loc</name>
         <load_address>0x2d8b</load_address>
         <run_address>0x2d8b</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_aranges</name>
         <load_address>0xc8</load_address>
         <run_address>0xc8</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_aranges</name>
         <load_address>0xe8</load_address>
         <run_address>0xe8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_aranges</name>
         <load_address>0x110</load_address>
         <run_address>0x110</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x1738</size>
         <contents>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-79"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1890</load_address>
         <run_address>0x1890</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-1c6"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-1c7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x17f8</load_address>
         <run_address>0x17f8</run_address>
         <size>0x98</size>
         <contents>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-152"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-190"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200628</run_address>
         <size>0x12</size>
         <contents>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-10c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x625</size>
         <contents>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-160"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-1cb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-187" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-188" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-189" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18a" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18b" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18c" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18e" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1aa" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1908</size>
         <contents>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-1cd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ac" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10812</size>
         <contents>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-1cc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ae" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x980</size>
         <contents>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-d0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b0" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xad44</size>
         <contents>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-163"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b2" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13ac</size>
         <contents>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-ee"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b4" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x627d</size>
         <contents>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b6" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2db1</size>
         <contents>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-b1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c0" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x138</size>
         <contents>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ca" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-1d8" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18c8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1d9" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x63a</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1da" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x18c8</used_space>
         <unused_space>0x1e738</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x1738</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x17f8</start_address>
               <size>0x98</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1890</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x18c8</start_address>
               <size>0x1e738</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x837</used_space>
         <unused_space>0x77c9</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-18c"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-18e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x625</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200625</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x20200628</start_address>
               <size>0x12</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020063a</start_address>
               <size>0x77c6</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1890</load_address>
            <load_size>0x11</load_size>
            <run_address>0x20200628</run_address>
            <run_size>0x12</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x18b0</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x625</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x18b8</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x18c8</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x18c8</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x18a4</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x18b0</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4e">
         <name>main</name>
         <value>0xba1</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-4f">
         <name>a</name>
         <value>0x2020062c</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-50">
         <name>TIMA0_IRQHandler</name>
         <value>0xabd</value>
         <object_component_ref idref="oc-3a"/>
      </symbol>
      <symbol id="sm-51">
         <name>key</name>
         <value>0x20200630</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-52">
         <name>keystate</name>
         <value>0x20200624</value>
      </symbol>
      <symbol id="sm-53">
         <name>pwm</name>
         <value>0x20200634</value>
         <object_component_ref idref="oc-6d"/>
      </symbol>
      <symbol id="sm-119">
         <name>SYSCFG_DL_init</name>
         <value>0xd21</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-11a">
         <name>SYSCFG_DL_initPower</name>
         <value>0x681</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-11b">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x4ed</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-11c">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xf8d</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-11d">
         <name>SYSCFG_DL_PWM_0_init</name>
         <value>0xcc9</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-11e">
         <name>SYSCFG_DL_PWM_1_init</name>
         <value>0xc6d</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-11f">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0xfcd</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-120">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0xf01</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-121">
         <name>SYSCFG_DL_UART_1_init</name>
         <value>0xd79</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-122">
         <name>SYSCFG_DL_UART_2_init</name>
         <value>0xdcd</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-123">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0x17cf</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-124">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x177d</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-125">
         <name>gPWM_0Backup</name>
         <value>0x20200400</value>
      </symbol>
      <symbol id="sm-126">
         <name>gPWM_1Backup</name>
         <value>0x20200578</value>
      </symbol>
      <symbol id="sm-127">
         <name>gTIMER_0Backup</name>
         <value>0x202004bc</value>
      </symbol>
      <symbol id="sm-128">
         <name>gUART_2Backup</name>
         <value>0x202005f0</value>
      </symbol>
      <symbol id="sm-129">
         <name>SYSCFG_DL_DMA_CH0_init</name>
         <value>0x1581</value>
         <object_component_ref idref="oc-154"/>
      </symbol>
      <symbol id="sm-134">
         <name>Default_Handler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-135">
         <name>Reset_Handler</name>
         <value>0x17ef</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-136">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-137">
         <name>NMI_Handler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-138">
         <name>HardFault_Handler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-139">
         <name>SVC_Handler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13a">
         <name>PendSV_Handler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13b">
         <name>SysTick_Handler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13c">
         <name>GROUP0_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13d">
         <name>GROUP1_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13e">
         <name>TIMG8_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13f">
         <name>ADC0_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-140">
         <name>ADC1_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-141">
         <name>CANFD0_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-142">
         <name>DAC0_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-143">
         <name>SPI0_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-144">
         <name>SPI1_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-145">
         <name>UART1_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-146">
         <name>UART2_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-147">
         <name>TIMG0_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-148">
         <name>TIMG6_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-149">
         <name>TIMA1_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14a">
         <name>TIMG7_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14b">
         <name>TIMG12_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14c">
         <name>I2C0_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14d">
         <name>I2C1_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14e">
         <name>AES_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14f">
         <name>RTC_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-150">
         <name>DMA_IRQHandler</name>
         <value>0x17e7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-171">
         <name>Systick_getTick</name>
         <value>0x17ad</value>
         <object_component_ref idref="oc-161"/>
      </symbol>
      <symbol id="sm-172">
         <name>delay_ms</name>
         <value>0x111d</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-173">
         <name>delay_us</name>
         <value>0x721</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-174">
         <name>UART0_IRQHandler</name>
         <value>0x114d</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-175">
         <name>UART3_IRQHandler</name>
         <value>0x10b9</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-18f">
         <name>UserKey</name>
         <value>0x20200628</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-198">
         <name>Set_PWM</name>
         <value>0x124d</value>
         <object_component_ref idref="oc-67"/>
      </symbol>
      <symbol id="sm-1c8">
         <name>OLED_Refresh_Gram</name>
         <value>0x9c5</value>
         <object_component_ref idref="oc-15f"/>
      </symbol>
      <symbol id="sm-1c9">
         <name>OLED_WR_Byte</name>
         <value>0xb31</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-1ca">
         <name>OLED_GRAM</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-1cb">
         <name>OLED_RS_Set</name>
         <value>0x168d</value>
         <object_component_ref idref="oc-159"/>
      </symbol>
      <symbol id="sm-1cc">
         <name>OLED_RS_Clr</name>
         <value>0x1679</value>
         <object_component_ref idref="oc-15a"/>
      </symbol>
      <symbol id="sm-1cd">
         <name>OLED_SCLK_Clr</name>
         <value>0x16a1</value>
         <object_component_ref idref="oc-15b"/>
      </symbol>
      <symbol id="sm-1ce">
         <name>OLED_SDIN_Set</name>
         <value>0x16dd</value>
         <object_component_ref idref="oc-15c"/>
      </symbol>
      <symbol id="sm-1cf">
         <name>OLED_SDIN_Clr</name>
         <value>0x16c9</value>
         <object_component_ref idref="oc-15d"/>
      </symbol>
      <symbol id="sm-1d0">
         <name>OLED_SCLK_Set</name>
         <value>0x16b5</value>
         <object_component_ref idref="oc-15e"/>
      </symbol>
      <symbol id="sm-1d1">
         <name>OLED_Clear</name>
         <value>0xc0d</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-1d2">
         <name>OLED_Init</name>
         <value>0x32d</value>
         <object_component_ref idref="oc-a4"/>
      </symbol>
      <symbol id="sm-1d3">
         <name>OLED_RST_Clr</name>
         <value>0x1651</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-1d4">
         <name>OLED_RST_Set</name>
         <value>0x1665</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-1d5">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d6">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d7">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d8">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d9">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1da">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1db">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1dc">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1dd">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1e6">
         <name>DL_Common_delayCycles</name>
         <value>0x17c5</value>
         <object_component_ref idref="oc-117"/>
      </symbol>
      <symbol id="sm-1f0">
         <name>DL_DMA_initChannel</name>
         <value>0xe21</value>
         <object_component_ref idref="oc-17e"/>
      </symbol>
      <symbol id="sm-20f">
         <name>DL_Timer_setClockConfig</name>
         <value>0x1411</value>
         <object_component_ref idref="oc-132"/>
      </symbol>
      <symbol id="sm-210">
         <name>DL_Timer_initTimerMode</name>
         <value>0x245</value>
         <object_component_ref idref="oc-13d"/>
      </symbol>
      <symbol id="sm-211">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x176d</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-212">
         <name>DL_Timer_initPWMMode</name>
         <value>0x5bd</value>
         <object_component_ref idref="oc-13a"/>
      </symbol>
      <symbol id="sm-213">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1521</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-214">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x13f5</value>
         <object_component_ref idref="oc-135"/>
      </symbol>
      <symbol id="sm-215">
         <name>DL_TimerA_initPWMMode</name>
         <value>0x945</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-222">
         <name>DL_UART_init</name>
         <value>0xeb9</value>
         <object_component_ref idref="oc-148"/>
      </symbol>
      <symbol id="sm-223">
         <name>DL_UART_setClockConfig</name>
         <value>0x1715</value>
         <object_component_ref idref="oc-142"/>
      </symbol>
      <symbol id="sm-231">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x411</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-232">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0xf49</value>
         <object_component_ref idref="oc-130"/>
      </symbol>
      <symbol id="sm-23d">
         <name>_c_int00_noargs</name>
         <value>0x129d</value>
         <object_component_ref idref="oc-5a"/>
      </symbol>
      <symbol id="sm-23e">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-24a">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1009</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-252">
         <name>_system_pre_init</name>
         <value>0x17f3</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-25d">
         <name>__TI_zero_init</name>
         <value>0x178d</value>
         <object_component_ref idref="oc-57"/>
      </symbol>
      <symbol id="sm-266">
         <name>__TI_decompress_none</name>
         <value>0x1739</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-271">
         <name>__TI_decompress_lzss</name>
         <value>0xa41</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-27b">
         <name>abort</name>
         <value>0x17e1</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-285">
         <name>HOSTexit</name>
         <value>0x17eb</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-286">
         <name>C$$EXIT</name>
         <value>0x17ea</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-28c">
         <name>__aeabi_fmul</name>
         <value>0x7b1</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-28d">
         <name>__mulsf3</name>
         <value>0x7b1</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-293">
         <name>__aeabi_fdiv</name>
         <value>0x8c1</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-294">
         <name>__divsf3</name>
         <value>0x8c1</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-29a">
         <name>__aeabi_f2iz</name>
         <value>0x1081</value>
         <object_component_ref idref="oc-108"/>
      </symbol>
      <symbol id="sm-29b">
         <name>__fixsfsi</name>
         <value>0x1081</value>
         <object_component_ref idref="oc-108"/>
      </symbol>
      <symbol id="sm-2a1">
         <name>__aeabi_ui2f</name>
         <value>0x1275</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-2a2">
         <name>__floatunsisf</name>
         <value>0x1275</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-2a8">
         <name>__aeabi_memcpy</name>
         <value>0x17d9</value>
         <object_component_ref idref="oc-4a"/>
      </symbol>
      <symbol id="sm-2a9">
         <name>__aeabi_memcpy4</name>
         <value>0x17d9</value>
         <object_component_ref idref="oc-4a"/>
      </symbol>
      <symbol id="sm-2aa">
         <name>__aeabi_memcpy8</name>
         <value>0x17d9</value>
         <object_component_ref idref="oc-4a"/>
      </symbol>
      <symbol id="sm-2b1">
         <name>__aeabi_memclr</name>
         <value>0x17b9</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
      <symbol id="sm-2b2">
         <name>__aeabi_memclr4</name>
         <value>0x17b9</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
      <symbol id="sm-2b3">
         <name>__aeabi_memclr8</name>
         <value>0x17b9</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
      <symbol id="sm-2b9">
         <name>__muldsi3</name>
         <value>0x1045</value>
         <object_component_ref idref="oc-16f"/>
      </symbol>
      <symbol id="sm-2c3">
         <name>TI_memcpy_small</name>
         <value>0x1727</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-2cc">
         <name>TI_memset_small</name>
         <value>0x179d</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-2cd">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2d0">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2d1">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
