#include "motor.h"
#include <math.h>

// MG996R舵机角度到PWM转换函数
int Angle_to_PWM_MG996R(float angle)
{
    // 限制角度范围在0-180度之间
    if (angle < 0.0f) {
        angle = 0.0f;
    } else if (angle > 180.0f) {
        angle = 180.0f;
    }

    // 线性插值计算PWM值
    // PWM = SERVO_PWM_MIN + (angle / 180.0) * (SERVO_PWM_MAX - SERVO_PWM_MIN)
    float pwm = SERVO_PWM_MIN + (angle / 180.0f) * (SERVO_PWM_MAX - SERVO_PWM_MIN);
    return (int)roundf(pwm);
}

// 设置舵机角度的便捷函数
void Set_Servo_Angle(float angle1, float angle2)
{
    int pwm1 = Angle_to_PWM_MG996R(angle1);
    int pwm2 = Angle_to_PWM_MG996R(angle2);
    Set_PWM(pwm1, pwm2);
}

// 原有PWM设置函数
void Set_PWM(int pwma,int pwmb)
{
    DL_Timer_setCaptureCompareValue(PWM_0_INST,pwma,GPIO_PWM_0_C0_IDX);
    DL_Timer_setCaptureCompareValue(PWM_1_INST,pwmb,GPIO_PWM_1_C1_IDX);
}


