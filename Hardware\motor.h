#ifndef _MOTOR_H
#define _MOTOR_H
#include "ti_msp_dl_config.h"
#include "board.h"

// MG996R舵机PWM控制参数
#define SERVO_PWM_MIN    500    // 1ms对应的PWM值 (0度)
#define SERVO_PWM_CENTER 750    // 1.5ms对应的PWM值 (90度)
#define SERVO_PWM_MAX    1000   // 2ms对应的PWM值 (180度)

// 角度到PWM转换函数
int Angle_to_PWM_MG996R(float angle);
void Set_Servo_Angle(float angle1, float angle2);

// 原有函数保留
float Position_PID_1(float Position,float Target);
float Position_PID_2(float Position,float Target);
void Set_PWM(int pwma,int pwmb);
uint16_t PWM_limit(uint16_t in,uint16_t max,uint16_t min);
#endif